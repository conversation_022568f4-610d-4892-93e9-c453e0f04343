name: Release

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag name for the release (e.g., v1.0.0-test)'
        required: true
        default: ''
        type: string

permissions:
  contents: write

env:
  CARGO_TERM_COLOR: always

jobs:
  checks:
    name: Run checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf libglib2.0-dev libgtk-3-dev

      - name: Install frontend dependencies
        run: npm ci

      #FIXME: reenable this check when all errors/warnings are resolved
      #- name: Check frontend
      #  run: npm run check

      - name: Build frontend
        run: npm run build

      - name: Check Rust formatting
        run: cargo fmt --all --check
        working-directory: src-tauri

      - name: Run Clippy
        run: cargo clippy --all-targets --all-features -- -D warnings
        working-directory: src-tauri

      - name: Run Rust tests
        run: cargo test
        working-directory: src-tauri

  build:
    name: Build and Release
    needs: checks
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: 'macos-latest'
            args: '--target aarch64-apple-darwin'
            target: 'aarch64-apple-darwin'
          - platform: 'ubuntu-22.04'
            args: '--target x86_64-unknown-linux-gnu'
            target: 'x86_64-unknown-linux-gnu'
          - platform: 'windows-latest'
            args: '--target x86_64-pc-windows-msvc'
            target: 'x86_64-pc-windows-msvc'

    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri

      - name: Install dependencies (Ubuntu only)
        if: matrix.platform == 'ubuntu-22.04'
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libayatana-appindicator3-dev librsvg2-dev patchelf libglib2.0-dev libgtk-3-dev

      - name: Install frontend dependencies
        run: npm ci

      - name: Build frontend
        run: npm run build

      - name: Configure Windows static linking
        if: matrix.platform == 'windows-latest'
        run: |
          echo "RUSTFLAGS=-C target-feature=+crt-static -C opt-level=3" >> $GITHUB_ENV
          echo "CARGO_BUILD_TARGET=x86_64-pc-windows-msvc" >> $GITHUB_ENV
          echo "CARGO_PROFILE_RELEASE_LTO=true" >> $GITHUB_ENV
        shell: bash

      - name: Set SDKROOT and CPLUS_INCLUDE_PATH for macOS
        if: matrix.platform == 'macos-latest'
        run: |
          export SDKROOT=$(xcrun --sdk macosx --show-sdk-path)
          echo "SDKROOT=$SDKROOT" >> $GITHUB_ENV
          echo "CPLUS_INCLUDE_PATH=$SDKROOT/usr/include/c++/v1" >> $GITHUB_ENV

      - name: Build Tauri app
        uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tagName: ${{ github.event.inputs.tag_name || github.ref_name }}
          releaseName: 'Colony ${{ github.event.inputs.tag_name || github.ref_name }}'
          releaseBody: |
            ## Colony v__VERSION__

            ### Installation

            **Linux:**
            - Download the `.AppImage` file for a portable application
            - Download the `.deb` file for Debian/Ubuntu systems
            - Download the `.rpm` file for Red Hat/Fedora systems

            **macOS:**
            - Download the `.dmg` file and drag Colony to your Applications folder

            **Windows:**
            - Download the `.msi` file and run the installer

            ### Changes
            See the commit history for detailed changes in this release.
          releaseDraft: false
          prerelease: false
          includeUpdaterJson: false
          args: ${{ matrix.args }}
          bundleIdentifier: 'com.colony.gui'

      - name: Verify build artifacts
        run: |
          echo "Checking build artifacts..."
          if [ "${{ matrix.platform }}" = "windows-latest" ]; then
            echo "Windows executable files:"
            find src-tauri/target -name "*.exe" -exec ls -lh {} \;
            echo "Windows installer files:"
            find . -name "*.msi" -exec ls -lh {} \;
            echo "Checking dependencies of main executable:"
            find src-tauri/target -name "colony.exe" -exec file {} \;
          elif [ "${{ matrix.platform }}" = "ubuntu-22.04" ]; then
            echo "Linux AppImage files:"
            find . -name "*.AppImage" -exec ls -lh {} \;
            echo "Linux DEB files:"
            find . -name "*.deb" -exec ls -lh {} \;
          elif [ "${{ matrix.platform }}" = "macos-latest" ]; then
            echo "macOS DMG files:"
            find . -name "*.dmg" -exec ls -lh {} \;
          fi
        shell: bash
