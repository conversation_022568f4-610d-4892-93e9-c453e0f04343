@import "tailwindcss";
@plugin "daisyui" {
  themes: all;
}

.h1 {
  @apply text-5xl font-extrabold;
}

.h2 {
  @apply text-4xl font-bold;
}

.h3 {
  @apply text-3xl font-bold;
}

.h4 {
  @apply text-2xl font-bold;
}

:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  /* color: #0f0f0f;
  background-color: #f6f6f6; */

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

/* @media (prefers-color-scheme: dark) {
  :root, body {
    color: #f6f6f6;
    background-color: #2f2f2f;
  }

  a, a:visited {
    color: #ffffff;
  }

  a:hover, a:focus {
    color: #ff981a;
    text-decoration: underline;
  }

  textarea, select, button.btn-neutral {
    color: #fbfbfb;
    background-color: #232326;
    border-color: #444;
  }

  input::placeholder, textarea::placeholder {
    color: #b5b5b5;
  }

  button.btn-neutral {
    background-color: #232326;
    border: 1px solid #444;
  }

  button:active {
    background-color: #33364d;
  }
} */