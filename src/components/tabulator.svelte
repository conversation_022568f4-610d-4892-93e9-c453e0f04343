<script>
  import { onMount, onD<PERSON>roy, createEventDispatcher } from 'svelte';
  import { TabulatorFull as Tabulator } from 'tabulator-tables';
  import { getCurrentWindow } from "@tauri-apps/api/window";
  import { DateTime } from "luxon";
  import { globalTheme } from '../stores/globals';

  export let columns, data, rowMenu, initialSort;

  const dispatch = createEventDispatcher();

  let tableComponent;
  let tabulatorInstance;

  // Export the tabulator instance so parent components can access it
  export { tabulatorInstance };
  let unlisten;
  let currentTheme = $globalTheme;
  let tableReady = false;
  let tableHeight = 300;

  function setTableHeight() {
    if (window) {
      tableHeight = Math.max(300, Math.min(window.innerHeight * 0.75, 1500));
    }
  }

  let resizeTimeout;

  function handleWindowResize() {
    // Debounce resize events to avoid excessive redraws
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      setTableHeight();

      // Dispatch a custom event to notify parent components about resize
      // This allows parent components to update column widths before table redraw
      window.dispatchEvent(new CustomEvent('tabulator-resize-start'));

      // Small delay to allow parent components to update columns
      setTimeout(() => {
        if (tabulatorInstance && tableReady) {
          // Update columns first (this will trigger the reactive statement)
          tabulatorInstance.setColumns(columns);
          // Then redraw the table to handle layout changes
          tabulatorInstance.redraw(true);
          // Recalculate column widths
          tabulatorInstance.recalcColumnWidths();
        }
      }, 10);
    }, 100); // 100ms debounce
  }

  function switchTabulatorTheme(theme) {
    const link = document.getElementById('tabulator-theme');
    if (link) {
      link.href = theme === 'dark'
        ? '/css/tabulator_midnight.min.css'
        : '/css/tabulator.min.css';
      if (tabulatorInstance) tabulatorInstance.redraw(true);
    }
  }

  onMount(async () => {
    setTableHeight();
    window.addEventListener('resize', handleWindowResize);

    tabulatorInstance = new Tabulator(tableComponent, {
      columns,
      height: tableHeight,
      minHeight: 300,
      data,
      rowContextMenu: rowMenu,
      reactiveData: false,
      layout: 'fitDataStretch',
      dependencies: {
        DateTime: DateTime,
      }, 
      initialSort: initialSort,
      tableBuilt: function() {
        tableReady = true;
      }
    });

    unlisten = await getCurrentWindow().onThemeChanged(({ payload: theme }) => {
      console.log("theme changed", theme)
      switchTabulatorTheme(theme);
    });
  });

  onDestroy(() => {
    if (unlisten) unlisten();
    if (tabulatorInstance) tabulatorInstance.destroy();
    window.removeEventListener('resize', handleWindowResize);
    clearTimeout(resizeTimeout);
  });

  $: if (tabulatorInstance && Array.isArray(data)) {
    tabulatorInstance.replaceData(data);
  }

  // Update columns when they change
  $: if (tabulatorInstance && columns && tableReady) {
    tabulatorInstance.setColumns(columns);
    // Force recalculation of column widths after setting new columns
    setTimeout(() => {
      if (tabulatorInstance) {
        tabulatorInstance.recalcColumnWidths();
      }
    }, 0);
  }

  $: if (typeof $globalTheme === 'string') {
    switchTabulatorTheme($globalTheme);
  }

  $: if (tabulatorInstance && tableReady && tableHeight) {
    tabulatorInstance.setHeight(tableHeight);
  }
</script>

<div bind:this={tableComponent}></div>