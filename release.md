# Colony GUI - v1.0.0 RELEASED!

After months of work, the first release of Colony is here!

## ✨ Features

- 🔐 **Secure Wallet Management** - BIP39 seed phrase generation and secure key storage
- 📁 **File Upload/Download** - Upload and download files easily
- 🔍 **Metadata Search** - Client-side search using simple text search
- 📁 **Pod Management** - Create and manage metadata for organizing and sharing content
- 🔗 **Decentralized Sharing** - Share files and metadata without a central database
- 🖥️ **Cross-Platform** - Native applications for Linux, Windows, and macOS*
- 🌐 **Dweb Built-in** - Dweb binary built-in for automatically opening Autonomi web apps
- 💻 **Local First** - For download only users, no crypto is required, install and go

## 📦 Installation

Precompiled binaries are available for various flavors of Linux as well as Windows on the github releases page [here](https://github.com/zettawatt/colony/releases/tag/v1.0.0-RC9). For our Mac friends we are unable to build binaries that will run on your computer due to Apple's ridicul... _wonderful_ "safety" features. The application does work well on a Mac, you just need to compile the program yourself. See the [README](https://github.com/zettawatt/colony/blob/main/README.md) for build instructions. One day I may shell out the $99 for the privilege of having some Apple certificates. Maybe.

## 🚀 Getting Started

All of the information needed to get setup can be found in the [User Manual](https://github.com/zettawatt/colony/blob/main/README.md#user-manual) in the repo's README. Check it out!

## 🌐 Dweb baked in

I added the latest dweb binaries as a side car in the Colony app. It automatically starts up when you login to the app. When you click on the download icon for a web app, it calls `dweb open` for you and pulls up the page in your web browser. Simple. Just make sure you don't have dweb already running in the background.

Your active wallet is used to initialize dweb. Note that if you switch active wallets, dweb will restart using that new wallet. So if you have web apps open when you switch wallets, that could be a problem, just an FYI.

## 🎉 But wait, there's more!

- 💳 **Wallet Management** - Manage multiple wallets for upload payments
- ⚙️ **Configuration** - download directory, change password, themes
- 📊 **Status Tracking** - track your upload and download progress

## 🔧 What's left?

We had to stop work to get this stable release out so the last week we've been crushing known bugs and getting to a stable point. The major enhancments we're still working on:
- 📥 **Download Streaming** - track progress and estimate remaining time
- 🔍 **Search Interface** - show more information per searched item with thumbnail support
- ℹ️ **Object Info** - show more information about individual objects before downloading
- 🏷️ **Metadata Helpers** - leverage `ia_downloader` mechanism for metadata enhancement
- 💰 **Wallet Balance** - simple to do, just didn't get to it yet
- ⚠️ **Error Handling** - better Autonomi error handling

## 🎯 We made it!

It has been a long journey with lots of sleepless nights and non-stop work, but we got this thing out by the deadline. In addition, I submit to the IF judges a whole suite of cross platform tools that lay the foundation and enhances the capabilities of the Colony GUI app:

- 📚 **[colonylib](https://github.com/zettawatt/colonylib)** - core functionality crate that others are using successfully such as [Mutant]() for content discovery
- 🔧 **[colonyd](https://github.com/zettawatt/colony-utils)** - a daemon exposing the colonylib API as REST endpoints
- 💻 **[colony](https://github.com/zettawatt/colony-utils)** - a parallel Colony CLI implementation that interacts with colonyd, for advanced users and scripting
- 📥 **[ia_downloader](https://github.com/zettawatt/colony-utils)** - Internet Archive bulk downloader and metadata composition tool
- 📤 **[colony_uploader](https://github.com/zettawatt/colony-utils)** - bulk data Autonomi uploader leveraging the colonyd REST API, with metrics tracking

There is more to do, but I'm very proud of what we've accomplished so far. So give it a try, let us know what works, what doesn't, and what could be better. Thank you everyone for your support so far, for today, finally, we can _Search Autonomi Simply_.
